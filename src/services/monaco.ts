import {
    type editor as <PERSON><PERSON><PERSON><PERSON>,
    EditorScopedLayoutService,
    IAccessibilityService,
    IAccessibilitySignalService,
    IClipboardService,
    ICodeEditorService,
    ICommandService,
    IConfigurationService,
    IContextKeyService,
    IContextMenuService,
    IContextViewService,
    IEditorProgressService,
    IEditorWorkerService,
    IHoverService,
    IInstantiationService,
    IKeybindingService,
    ILanguageConfigurationService,
    ILanguageFeaturesService,
    ILanguageService,
    ILayoutService,
    IModelService,
    INotificationService,
    IOpenerService,
    IQuickInputService,
    IStandaloneThemeService,
    OpenerService,
    ServiceCollection,
    StandaloneDiffEditor,
    StandaloneEditor,
    StandaloneServices,
    StaticServices,
} from 'mo/monaco';
import { inject, injectable } from 'tsyringe';

import { ColorThemeService } from './colorTheme';

type IEditorOverrideServices = MonacoEditor.IEditorOverrideServices;

@injectable()
export class MonacoService {
    private _services: ServiceCollection;
    private _container!: HTMLElement | null;
    private _hiddenEditor: MonacoEditor.IStandaloneCodeEditor | null = null;
    private _hiddenEditorContainer: HTMLElement | null = null;

    constructor(@inject('colorTheme') private colorTheme: ColorThemeService) {}

    public initWorkspace(container: HTMLElement) {
        this._container = container;
        this._services = this.createStandaloneServices();
    }

    get container() {
        return this._container;
    }

    get services() {
        return this._services;
    }

    get commandService() {
        return this.services.get(ICommandService);
    }

    get QuickInputService(): IQuickInputService {
        return this.services.get(IQuickInputService);
    }

    private mergeEditorServices(overrides?: IEditorOverrideServices) {
        if (overrides) {
            const services = this.services;
            for (const serviceId in overrides) {
                if (serviceId) {
                    const service = services.get(serviceId);
                    if (service && overrides[serviceId]) {
                        services.set(serviceId, overrides[serviceId]);
                    }
                }
            }
        }
    }

    public create(
        domElement: HTMLElement,
        options?: MonacoEditor.IStandaloneEditorConstructionOptions,
        overrides?: IEditorOverrideServices
    ): MonacoEditor.IStandaloneCodeEditor {
        const services = this.services;

        this.mergeEditorServices(overrides);

        const standaloneEditor = new StandaloneEditor(
            domElement,
            options,
            services.get(IInstantiationService),
            services.get(ICodeEditorService),
            services.get(ICommandService),
            services.get(IContextKeyService),
            services.get(IHoverService),
            services.get(IKeybindingService),
            services.get(IStandaloneThemeService),
            services.get(INotificationService),
            services.get(IConfigurationService),
            services.get(IAccessibilityService),
            services.get(IModelService),
            services.get(ILanguageService),
            services.get(ILanguageConfigurationService),
            services.get(ILanguageFeaturesService)
        );

        // Should be called after the editor is created
        this.colorTheme.setCurrent(this.colorTheme.getCurrent());

        return standaloneEditor;
    }

    public createDiffEditor(
        domElement: HTMLElement,
        options?: MonacoEditor.IStandaloneDiffEditorConstructionOptions,
        overrides?: IEditorOverrideServices
    ): MonacoEditor.IStandaloneDiffEditor {
        const services = this.services;

        this.mergeEditorServices(overrides);

        const standaloneDiffEditor = new StandaloneDiffEditor(
            domElement,
            options,
            services.get(IInstantiationService),
            services.get(IContextKeyService),
            services.get(ICodeEditorService),
            services.get(IStandaloneThemeService),
            services.get(INotificationService),
            services.get(IConfigurationService),
            services.get(IContextMenuService),
            services.get(IEditorProgressService),
            services.get(IClipboardService),
            services.get(IAccessibilitySignalService)
        );

        // Should be called after the editor is created
        this.colorTheme.setCurrent(this.colorTheme.getCurrent());

        return standaloneDiffEditor;
    }

    // When Application will unmount, call it
    public dispose() {
        // Clean up hidden editor
        if (this._hiddenEditor) {
            this._hiddenEditor.dispose();
            this._hiddenEditor = null;
        }
        if (this._hiddenEditorContainer && this._hiddenEditorContainer.parentNode) {
            this._hiddenEditorContainer.parentNode.removeChild(this._hiddenEditorContainer);
            this._hiddenEditorContainer = null;
        }
    }

    /**
     * Create a hidden editor to ensure QuickInputService always has a focused editor context
     * This provides a more reliable solution than patching Monaco's internal services
     */
    private createHiddenEditor() {
        try {
            // Create a hidden container for the editor
            this._hiddenEditorContainer = document.createElement('div');
            this._hiddenEditorContainer.style.position = 'fixed';
            this._hiddenEditorContainer.style.left = '0';
            this._hiddenEditorContainer.style.top = '0';
            this._hiddenEditorContainer.style.width = '100vw';
            this._hiddenEditorContainer.style.height = '50vh';
            this._hiddenEditorContainer.style.opacity = '1';
            // this._hiddenEditorContainer.style.pointerEvents = 'none';
            this._hiddenEditorContainer.style.zIndex = '9999';
            this._hiddenEditorContainer.setAttribute('aria-hidden', 'true');
            document.body.appendChild(this._hiddenEditorContainer);

            // Create the hidden editor with minimal configuration
            this._hiddenEditor = new StandaloneEditor(
                this._hiddenEditorContainer,
                {
                    value: '',
                    language: 'plaintext',
                    theme: 'vs-dark',
                    readOnly: true,
                    minimap: { enabled: false },
                    scrollbar: { vertical: 'hidden', horizontal: 'hidden' },
                    lineNumbers: 'off',
                    glyphMargin: false,
                    folding: false,
                    lineDecorationsWidth: 0,
                    lineNumbersMinChars: 0,
                    renderLineHighlight: 'none',
                    scrollBeyondLastLine: false,
                    wordWrap: 'off',
                    overviewRulerLanes: 0,
                    hideCursorInOverviewRuler: true,
                    overviewRulerBorder: false,
                    renderValidationDecorations: 'off',
                },
                this.services.get(IInstantiationService),
                this.services.get(ICodeEditorService),
                this.services.get(ICommandService),
                this.services.get(IContextKeyService),
                this.services.get(IHoverService),
                this.services.get(IKeybindingService),
                this.services.get(IStandaloneThemeService),
                this.services.get(INotificationService),
                this.services.get(IConfigurationService),
                this.services.get(IAccessibilityService),
                this.services.get(IModelService),
                this.services.get(ILanguageService),
                this.services.get(ILanguageConfigurationService),
                this.services.get(ILanguageFeaturesService)
            );

            console.log('Successfully created hidden editor for QuickInputService');
        } catch (error) {
            console.warn('Failed to create hidden editor:', error);
        }
    }

    /**
     * Ensure the hidden editor has focus when QuickInputService operations are needed
     * This method should be called before any QuickInputService operations
     */
    public ensureQuickInputContext(): void {
        if (this._hiddenEditor && this._hiddenEditorContainer) {
            try {
                // Focus the hidden editor to provide context for QuickInputService
                this._hiddenEditor.focus();
                console.log('MonacoService: Hidden editor focused for QuickInputService context');
            } catch (error) {
                console.warn('Failed to focus hidden editor:', error);
            }
        } else {
            console.warn('MonacoService: Hidden editor not available, QuickInputService may not work properly');
        }
    }

    /**
     * Get the hidden editor instance (for debugging purposes)
     */
    public getHiddenEditor() {
        return this._hiddenEditor;
    }

    private createStandaloneServices(): ServiceCollection {
        StandaloneServices.initialize({});

        const services = new ServiceCollection();
        const serviceIds = [
            IInstantiationService,
            ICodeEditorService,
            ICommandService,
            IConfigurationService,
            IContextKeyService,
            IKeybindingService,
            IContextViewService,
            IStandaloneThemeService,
            INotificationService,
            IAccessibilityService,
            IAccessibilitySignalService,
            IModelService,
            ILanguageService,
            ILanguageConfigurationService,
            ILanguageFeaturesService,
            IHoverService,
            IEditorWorkerService,
            IContextMenuService,
            IEditorProgressService,
            IClipboardService,
            // Note: We intentionally exclude IQuickInputService here to avoid getting the default StandaloneQuickInputService
        ];

        serviceIds.forEach((serviceId) => {
            const service = StandaloneServices.get(serviceId);
            if (service) {
                services.set(serviceId, service);
            }
        });

        if (!services.get(IOpenerService)) {
            services.set(
                IOpenerService,
                new OpenerService(services.get(ICodeEditorService), services.get(ICommandService))
            );
        }

        const layoutService = new EditorScopedLayoutService(
            this.container,
            StaticServices.codeEditorService.get(ICodeEditorService)
        );

        // Override layoutService
        services.set(ILayoutService, layoutService);

        // Get the QuickInputService
        const quickInputService = StandaloneServices.get(IQuickInputService);
        if (quickInputService) {
            services.set(IQuickInputService, quickInputService);
        }

        // Override dispose for prevent disposed by instance
        this.dispose = services.dispose;
        services.dispose = () => {};

        // Create hidden editor after services are set up
        // Use setTimeout to ensure DOM is ready and services are fully initialized
        setTimeout(() => {
            this.createHiddenEditor();
        }, 0);

        return services;
    }
}
