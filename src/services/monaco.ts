import {
    type editor as <PERSON><PERSON><PERSON><PERSON>,
    EditorScopedLayoutService,
    IAccessibilityService,
    IAccessibilitySignalService,
    IClipboardService,
    ICodeEditorService,
    ICommandService,
    IConfigurationService,
    IContextKeyService,
    IContextMenuService,
    IContextViewService,
    IEditorProgressService,
    IEditorWorkerService,
    IHoverService,
    IInstantiationService,
    IKeybindingService,
    ILanguageConfigurationService,
    ILanguageFeaturesService,
    ILanguageService,
    ILayoutService,
    IModelService,
    INotificationService,
    IOpenerService,
    IQuickInputService,
    IStandaloneThemeService,
    OpenerService,
    ServiceCollection,
    StandaloneDiffEditor,
    StandaloneEditor,
    StandaloneServices,
    StaticServices,
} from 'mo/monaco';
import { inject, injectable } from 'tsyringe';

import { ColorThemeService } from './colorTheme';

type IEditorOverrideServices = MonacoEditor.IEditorOverrideServices;

@injectable()
export class MonacoService {
    private _services: ServiceCollection;
    private _container!: HTMLElement | null;

    constructor(@inject('colorTheme') private colorTheme: ColorThemeService) {}

    public initWorkspace(container: HTMLElement) {
        this._container = container;
        this._services = this.createStandaloneServices();
    }

    get container() {
        return this._container;
    }

    get services() {
        return this._services;
    }

    get commandService() {
        return this.services.get(ICommandService);
    }

    get QuickInputService(): IQuickInputService {
        return this.services.get(IQuickInputService);
    }

    private mergeEditorServices(overrides?: IEditorOverrideServices) {
        if (overrides) {
            const services = this.services;
            for (const serviceId in overrides) {
                if (serviceId) {
                    const service = services.get(serviceId);
                    if (service && overrides[serviceId]) {
                        services.set(serviceId, overrides[serviceId]);
                    }
                }
            }
        }
    }

    public create(
        domElement: HTMLElement,
        options?: MonacoEditor.IStandaloneEditorConstructionOptions,
        overrides?: IEditorOverrideServices
    ): MonacoEditor.IStandaloneCodeEditor {
        const services = this.services;

        this.mergeEditorServices(overrides);

        const standaloneEditor = new StandaloneEditor(
            domElement,
            options,
            services.get(IInstantiationService),
            services.get(ICodeEditorService),
            services.get(ICommandService),
            services.get(IContextKeyService),
            services.get(IHoverService),
            services.get(IKeybindingService),
            services.get(IStandaloneThemeService),
            services.get(INotificationService),
            services.get(IConfigurationService),
            services.get(IAccessibilityService),
            services.get(IModelService),
            services.get(ILanguageService),
            services.get(ILanguageConfigurationService),
            services.get(ILanguageFeaturesService)
        );

        // Should be called after the editor is created
        this.colorTheme.setCurrent(this.colorTheme.getCurrent());

        return standaloneEditor;
    }

    public createDiffEditor(
        domElement: HTMLElement,
        options?: MonacoEditor.IStandaloneDiffEditorConstructionOptions,
        overrides?: IEditorOverrideServices
    ): MonacoEditor.IStandaloneDiffEditor {
        const services = this.services;

        this.mergeEditorServices(overrides);

        const standaloneDiffEditor = new StandaloneDiffEditor(
            domElement,
            options,
            services.get(IInstantiationService),
            services.get(IContextKeyService),
            services.get(ICodeEditorService),
            services.get(IStandaloneThemeService),
            services.get(INotificationService),
            services.get(IConfigurationService),
            services.get(IContextMenuService),
            services.get(IEditorProgressService),
            services.get(IClipboardService),
            services.get(IAccessibilitySignalService)
        );

        // Should be called after the editor is created
        this.colorTheme.setCurrent(this.colorTheme.getCurrent());

        return standaloneDiffEditor;
    }

    // When Application will unmount, call it
    public dispose() {}

    /**
     * Patch Monaco's StandaloneQuickInputService to not require focused editor
     * This directly modifies the prototype to bypass the focused editor requirement
     */
    private patchStandaloneQuickInputService() {
        try {
            // Get the StandaloneQuickInputService from Monaco's internal modules
            const standaloneQuickInputService = StandaloneServices.get(IQuickInputService);
            if (!standaloneQuickInputService) {
                console.warn('Could not find StandaloneQuickInputService to patch');
                return;
            }

            // Get the constructor/class
            const ServiceClass = standaloneQuickInputService.constructor;

            // Store original activeService getter
            const originalActiveServiceDescriptor = Object.getOwnPropertyDescriptor(
                ServiceClass.prototype,
                'activeService'
            );

            if (originalActiveServiceDescriptor && originalActiveServiceDescriptor.get) {
                const originalGetter = originalActiveServiceDescriptor.get;

                // Replace the activeService getter with our patched version
                Object.defineProperty(ServiceClass.prototype, 'activeService', {
                    get: function () {
                        try {
                            // Try to get the original active service (which requires focused editor)
                            return originalGetter.call(this);
                        } catch (error) {
                            // If it fails with the specific "focused editor" error, provide fallback
                            if (
                                error instanceof Error &&
                                error.message === 'Quick input service needs a focused editor to work.'
                            ) {
                                console.log('QuickInputService: Using fallback service (no focused editor required)');

                                // Create a fallback service if we don't have one
                                if (!this._moleculeFallbackService) {
                                    // Create a minimal but functional QuickInputService implementation
                                    this._moleculeFallbackService = this._createFallbackService();
                                }
                                return this._moleculeFallbackService;
                            }
                            // Re-throw other errors
                            throw error;
                        }
                    },
                    configurable: true,
                });

                // Add a method to create the fallback service
                ServiceClass.prototype._createFallbackService = function () {
                    const container = document.body;
                    const layoutService = new EditorScopedLayoutService(container, this.codeEditorService);

                    // Create a real EditorScopedQuickInputService but with document.body as container
                    try {
                        // Try to create a proper EditorScopedQuickInputService
                        return this.instantiationService.createInstance(
                            (window as any).monaco?.editor?.EditorScopedQuickInputService ||
                                require('monaco-editor/esm/vs/editor/standalone/browser/quickInput/standaloneQuickInputService')
                                    .EditorScopedQuickInputService,
                            { getContainerDomNode: () => container }, // Mock editor
                            this.instantiationService,
                            undefined, // contextKeyService
                            undefined, // themeService
                            layoutService,
                            undefined // configurationService
                        );
                    } catch (fallbackError) {
                        console.warn(
                            'Failed to create proper fallback service, using minimal implementation:',
                            fallbackError
                        );
                        // Return a minimal implementation as last resort
                        return {
                            createQuickPick: (_options?: any) => ({
                                items: [],
                                activeItems: [],
                                placeholder: '',
                                canSelectMany: false,
                                show: () => {},
                                hide: () => {},
                                dispose: () => {},
                                onDidAccept: (_callback: any) => ({ dispose: () => {} }),
                                onDidHide: (_callback: any) => ({ dispose: () => {} }),
                                onDidChangeActive: (_callback: any) => ({ dispose: () => {} }),
                                onDidChangeSelection: (_callback: any) => ({ dispose: () => {} }),
                            }),
                            createInputBox: () => ({
                                value: '',
                                placeholder: '',
                                show: () => {},
                                hide: () => {},
                                dispose: () => {},
                                onDidAccept: (_callback: any) => ({ dispose: () => {} }),
                                onDidHide: (_callback: any) => ({ dispose: () => {} }),
                                onDidChangeValue: (_callback: any) => ({ dispose: () => {} }),
                            }),
                            pick: () => Promise.resolve(undefined),
                            currentQuickInput: null,
                            quickAccess: null,
                        };
                    }
                };

                console.log('Successfully patched StandaloneQuickInputService.activeService getter');
            } else {
                console.warn('Could not find activeService getter to patch');
            }
        } catch (error) {
            console.warn('Failed to patch StandaloneQuickInputService:', error);
        }
    }

    private createStandaloneServices(): ServiceCollection {
        StandaloneServices.initialize({});

        // Patch Monaco's StandaloneQuickInputService immediately after initialization
        this.patchStandaloneQuickInputService();

        const services = new ServiceCollection();
        const serviceIds = [
            IInstantiationService,
            ICodeEditorService,
            ICommandService,
            IConfigurationService,
            IContextKeyService,
            IKeybindingService,
            IContextViewService,
            IStandaloneThemeService,
            INotificationService,
            IAccessibilityService,
            IAccessibilitySignalService,
            IModelService,
            ILanguageService,
            ILanguageConfigurationService,
            ILanguageFeaturesService,
            IHoverService,
            IEditorWorkerService,
            IContextMenuService,
            IEditorProgressService,
            IClipboardService,
            // Note: We intentionally exclude IQuickInputService here to avoid getting the default StandaloneQuickInputService
        ];

        serviceIds.forEach((serviceId) => {
            const service = StandaloneServices.get(serviceId);
            if (service) {
                services.set(serviceId, service);
            }
        });

        if (!services.get(IOpenerService)) {
            services.set(
                IOpenerService,
                new OpenerService(services.get(ICodeEditorService), services.get(ICommandService))
            );
        }

        const layoutService = new EditorScopedLayoutService(
            this.container,
            StaticServices.codeEditorService.get(ICodeEditorService)
        );

        // Override layoutService
        services.set(ILayoutService, layoutService);

        // Get the (now patched) QuickInputService
        const quickInputService = StandaloneServices.get(IQuickInputService);
        if (quickInputService) {
            services.set(IQuickInputService, quickInputService);
        }

        // Override dispose for prevent disposed by instance
        this.dispose = services.dispose;
        services.dispose = () => {};
        return services;
    }
}
