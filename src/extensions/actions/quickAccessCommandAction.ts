import { BaseAction } from 'mo/glue/baseAction';
import { ICodeEditorService, IQuickInputService, KeyCode, KeyMod, ServicesAccessor } from 'mo/monaco';
import { IMoleculeContext, KeybindingWeight } from 'mo/types';

import { getGlobalMonacoService } from './index';

// TODO: migrate CommandQuickAccessProvider

export class QuickAccessCommandAction extends BaseAction {
    static readonly ID = 'menuBar.item.commandPalette';
    static PREFIX = '>';

    constructor(private molecule: IMoleculeContext) {
        super({
            id: QuickAccessCommandAction.ID,
            label: molecule.locale.localize('menuBar.item.commandPalette', 'Command Palette'),
            title: molecule.locale.localize('menuBar.item.commandPalette', 'Command Palette'),
            alias: 'Command Palette',
            f1: false,
            keybinding: {
                weight: KeybindingWeight.WorkbenchContrib,
                when: undefined,
                primary: KeyMod.CtrlCmd | KeyMod.Shift | KeyCode.KeyP,
                secondary: [KeyCode.F1],
            },
        });
    }

    run(accessor: ServicesAccessor): void {
        // First try to use the global MonacoService to ensure QuickInput context
        const monacoService = getGlobalMonacoService();
        if (monacoService) {
            monacoService.ensureQuickInputContext();
        } else {
            // Fallback to the local method if global service is not available
            this.ensureQuickInputContext(accessor);
        }

        accessor.get(IQuickInputService).quickAccess.show(QuickAccessCommandAction.PREFIX);
    }

    /**
     * Ensure QuickInputService has proper context by checking if any editor is focused
     * If not, try to focus an existing editor or handle the case gracefully
     */
    private ensureQuickInputContext(accessor: ServicesAccessor): void {
        try {
            const codeEditorService = accessor.get(ICodeEditorService);

            // Try to get the focused editor using the correct Monaco API
            const focusedEditor = (codeEditorService as any).getFocusedCodeEditor?.();
            if (focusedEditor) {
                // Already have a focused editor, no need to do anything
                return;
            }

            // Try to get any available editor and focus it
            const editors = (codeEditorService as any).listCodeEditors?.() || [];
            if (editors.length > 0) {
                // Focus the first available editor
                editors[0].focus();
                return;
            }

            // If no editors are available, the QuickInputService might still work
            // depending on the Monaco version and configuration
            console.log('QuickAccessCommandAction: No editors available, proceeding anyway');
        } catch (error) {
            console.warn('QuickAccessCommandAction: Failed to ensure QuickInput context:', error);
            // Continue anyway, let the QuickInputService handle the error
        }
    }
}
